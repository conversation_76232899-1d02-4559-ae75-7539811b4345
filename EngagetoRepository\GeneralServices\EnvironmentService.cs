﻿using EngagetoContracts.GeneralContracts;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;

namespace EngagetoRepository.GeneralServices
{
    public class EnvironmentService : IEnvironmentService
    {
        private readonly IConfiguration _configuration;
        private readonly IHostEnvironment _hostEnvironment;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public EnvironmentService(IConfiguration configuration, IHostEnvironment hostEnvironment, IHttpContextAccessor httpContextAccessor)
        {
            _configuration = configuration;
            _hostEnvironment = hostEnvironment;
            _httpContextAccessor = httpContextAccessor;
        }

        public bool IsDevelopment
        {
            get
            {
                // First try to get from HTTP context if available (for regular HTTP requests)
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext != null)
                {
                    var requestUrl = $"{httpContext.Request.Scheme}://{httpContext.Request.Host}{httpContext.Request.Path}";
                    return !requestUrl.Contains("connect.engageto.in");
                }

                // Fallback to host environment for SignalR and other scenarios
                return _hostEnvironment.IsDevelopment();
            }
            set { } // Keep setter for backward compatibility with middleware
        }

        public HostString RequestHost
        {
            get
            {
                var httpContext = _httpContextAccessor.HttpContext;
                return httpContext?.Request.Host ?? new HostString();
            }
            set { } // Keep setter for backward compatibility with middleware
        }

        public string? RequestScheme
        {
            get
            {
                var httpContext = _httpContextAccessor.HttpContext;
                return httpContext?.Request.Scheme ?? string.Empty;
            }
            set { } // Keep setter for backward compatibility with middleware
        }

        public string GetDevWebsiteLink()
        {
            return _configuration["Origins:DevWebsiteMain"];
        }
        public string GetProdWebsiteLink()
        {
            return _configuration["Origins:ProdWebsiteMain"];
        }
    }
}
